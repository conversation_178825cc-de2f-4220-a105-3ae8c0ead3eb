# Augment Code OAuth 认证工具

这是一个用于 Augment Code OAuth 认证的本地服务器版本工具，提供完全自动化的认证流程。

## 🚀 功能特性

- ✅ **全自动化流程**: 自动启动本地服务器、打开浏览器、处理回调
- 🔐 **PKCE 安全认证**: 使用 OAuth 2.0 PKCE 流程确保安全性
- 💾 **令牌管理**: 自动保存和加载访问令牌
- 🌐 **用户友好**: 清晰的控制台输出和浏览器反馈页面
- ⚙️ **灵活配置**: 支持自定义端口和配置文件路径

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests
```

## 🎯 使用方法

### 基本使用

```bash
python augment_auth.py
```

### 高级选项

```bash
# 指定自定义端口
python augment_auth.py --port 9000

# 指定自定义配置文件
python augment_auth.py --config my_tokens.json

# 显示完整访问令牌
python augment_auth.py --show-token

# 查看帮助
python augment_auth.py --help
```

## 📋 使用流程

1. **运行程序**: 执行 `python augment_auth.py`
2. **自动打开浏览器**: 程序会自动打开默认浏览器并跳转到授权页面
3. **完成授权**: 在浏览器中登录并授权应用访问
4. **自动处理**: 程序自动接收授权码并交换访问令牌
5. **保存令牌**: 访问令牌自动保存到本地文件供后续使用

## 🔧 配置说明

### 令牌文件格式

程序会在当前目录生成 `tokens.json` 文件，包含以下信息：

```json
{
  "access_token": "your_access_token_here",
  "token_type": "Bearer",
  "scope": "email",
  "expires_in": 3600
}
```

### 端口配置

- **默认端口**: 8080
- **回调地址**: `http://localhost:8080/callback`
- **可自定义**: 使用 `--port` 参数指定其他端口

## 🔐 安全注意事项

1. **令牌文件权限**: 程序会自动设置令牌文件为仅所有者可读写（Unix系统）
2. **本地服务器**: 服务器仅监听本地地址，不对外开放
3. **PKCE 流程**: 使用标准的 OAuth 2.0 PKCE 流程确保安全性
4. **状态验证**: 自动验证 state 参数防止 CSRF 攻击

## 🛠️ API 调用示例

认证成功后，您可以使用保存的令牌调用 Augment Code API：

### Python 示例

```python
import json
import requests

# 加载令牌
with open('tokens.json', 'r') as f:
    tokens = json.load(f)

# 使用令牌调用API
headers = {
    'Authorization': f"Bearer {tokens['access_token']}",
    'Content-Type': 'application/json'
}

# 示例API调用（请根据实际API文档调整）
response = requests.get(
    'https://api.augmentcode.com/user/profile',
    headers=headers
)

if response.status_code == 200:
    user_info = response.json()
    print("用户信息:", user_info)
else:
    print("API调用失败:", response.status_code, response.text)
```

### curl 示例

```bash
# 从令牌文件中提取访问令牌
ACCESS_TOKEN=$(cat tokens.json | python -c "import sys, json; print(json.load(sys.stdin)['access_token'])")

# 使用令牌调用API
curl -H "Authorization: Bearer $ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     https://api.augmentcode.com/user/profile
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```
   解决方案: 使用 --port 参数指定其他端口
   python augment_auth.py --port 9000
   ```

2. **浏览器未自动打开**
   ```
   解决方案: 手动复制控制台显示的URL到浏览器中打开
   ```

3. **令牌交换失败**
   ```
   可能原因: 
   - 网络连接问题
   - Augment Code服务器问题
   - token端点URL不正确
   ```

4. **授权被拒绝**
   ```
   解决方案: 检查Augment Code账户状态，重新尝试授权
   ```

### 调试模式

如果遇到问题，可以查看详细的错误信息：

```bash
python augment_auth.py --show-token
```

## 📝 文件说明

- `augment_auth.py` - 主程序文件
- `requirements.txt` - Python依赖列表
- `tokens.json` - 令牌存储文件（运行时生成）
- `README.md` - 使用说明文档

## 🔄 令牌刷新

目前版本不支持自动令牌刷新。如果令牌过期，请重新运行程序进行认证。

## 📞 技术支持

如果您在使用过程中遇到问题，请检查：

1. Python版本（建议 3.7+）
2. 网络连接状态
3. 防火墙设置（确保本地端口可访问）
4. Augment Code服务状态

## 📄 许可证

本工具仅供学习和个人使用。请遵守 Augment Code 的服务条款。
