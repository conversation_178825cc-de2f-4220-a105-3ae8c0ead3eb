#!/usr/bin/env python3
"""
Augment Code API 使用示例
演示如何使用认证后的令牌调用 API
"""

import json
import requests
import os


def load_tokens(token_file="tokens.json"):
    """加载保存的令牌"""
    try:
        if not os.path.exists(token_file):
            print(f"❌ 令牌文件 {token_file} 不存在")
            print("请先运行 augment_auth.py 进行认证")
            return None
        
        with open(token_file, 'r', encoding='utf-8') as f:
            tokens = json.load(f)
        
        if 'access_token' not in tokens:
            print("❌ 令牌文件格式错误，缺少 access_token")
            return None
        
        return tokens
    
    except Exception as e:
        print(f"❌ 加载令牌失败: {e}")
        return None


def make_api_request(endpoint, access_token, method="GET", data=None):
    """发送API请求"""
    headers = {
        'Authorization': f"Bearer {access_token}",
        'Content-Type': 'application/json',
        'User-Agent': 'Augment-OAuth-Tool/1.0'
    }
    
    try:
        if method.upper() == "GET":
            response = requests.get(endpoint, headers=headers, timeout=30)
        elif method.upper() == "POST":
            response = requests.post(endpoint, headers=headers, json=data, timeout=30)
        else:
            print(f"❌ 不支持的HTTP方法: {method}")
            return None
        
        return response
    
    except requests.exceptions.RequestException as e:
        print(f"❌ API请求失败: {e}")
        return None


def example_api_calls():
    """示例API调用"""
    print("🔐 Augment Code API 使用示例")
    print("=" * 40)
    
    # 加载令牌
    tokens = load_tokens()
    if not tokens:
        return
    
    access_token = tokens['access_token']
    print(f"✅ 令牌加载成功: {access_token[:20]}...")
    
    # 示例API端点（请根据实际API文档调整）
    api_endpoints = [
        {
            "name": "用户信息",
            "url": "https://api.augmentcode.com/user/profile",
            "method": "GET"
        },
        {
            "name": "用户设置",
            "url": "https://api.augmentcode.com/user/settings",
            "method": "GET"
        },
        {
            "name": "项目列表",
            "url": "https://api.augmentcode.com/projects",
            "method": "GET"
        }
    ]
    
    print(f"\n📡 开始测试API调用...")
    
    for endpoint_info in api_endpoints:
        print(f"\n🔍 测试: {endpoint_info['name']}")
        print(f"   URL: {endpoint_info['url']}")
        
        response = make_api_request(
            endpoint_info['url'],
            access_token,
            endpoint_info['method']
        )
        
        if response:
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ 成功获取数据")
                    print(f"   响应长度: {len(str(data))} 字符")
                    
                    # 显示部分响应内容（避免输出过多）
                    if isinstance(data, dict):
                        keys = list(data.keys())[:3]  # 只显示前3个键
                        print(f"   响应键: {keys}")
                    
                except json.JSONDecodeError:
                    print(f"   ✅ 请求成功，但响应不是JSON格式")
                    print(f"   响应内容: {response.text[:100]}...")
            
            elif response.status_code == 401:
                print(f"   ❌ 认证失败，令牌可能已过期")
                print(f"   建议重新运行 augment_auth.py 进行认证")
            
            elif response.status_code == 403:
                print(f"   ❌ 权限不足")
            
            elif response.status_code == 404:
                print(f"   ⚠️ API端点不存在（这是正常的，因为这些是示例端点）")
            
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                print(f"   错误信息: {response.text[:200]}...")
        
        else:
            print(f"   ❌ 无法连接到API")
    
    print(f"\n" + "=" * 40)
    print("📝 注意事项:")
    print("1. 上述API端点仅为示例，请根据实际的Augment Code API文档调整")
    print("2. 如果遇到401错误，请重新运行认证程序")
    print("3. 如果遇到404错误，说明API端点不存在（这是正常的）")
    print("4. 请查阅官方API文档获取正确的端点信息")


def check_token_validity():
    """检查令牌有效性"""
    print("🔍 检查令牌有效性...")
    
    tokens = load_tokens()
    if not tokens:
        return False
    
    # 显示令牌信息
    print(f"令牌类型: {tokens.get('token_type', 'Unknown')}")
    print(f"权限范围: {tokens.get('scope', 'Unknown')}")
    
    if 'expires_in' in tokens:
        print(f"有效期: {tokens['expires_in']} 秒")
    
    # 尝试一个简单的API调用来验证令牌
    test_endpoints = [
        "https://api.augmentcode.com/health",
        "https://api.augmentcode.com/user/profile",
        "https://auth.augmentcode.com/userinfo"
    ]
    
    for endpoint in test_endpoints:
        print(f"\n测试端点: {endpoint}")
        response = make_api_request(endpoint, tokens['access_token'])
        
        if response:
            if response.status_code == 200:
                print("✅ 令牌有效")
                return True
            elif response.status_code == 401:
                print("❌ 令牌无效或已过期")
                return False
            elif response.status_code == 404:
                print("⚠️ 端点不存在，但令牌格式正确")
                continue
            else:
                print(f"⚠️ 未知状态: {response.status_code}")
                continue
    
    print("⚠️ 无法确定令牌有效性")
    return None


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Augment Code API 使用示例')
    parser.add_argument('--check', action='store_true', help='检查令牌有效性')
    parser.add_argument('--token-file', default='tokens.json', help='令牌文件路径')
    
    args = parser.parse_args()
    
    if args.check:
        check_token_validity()
    else:
        example_api_calls()


if __name__ == "__main__":
    main()
