#!/usr/bin/env python3
"""
Augment Code OAuth 认证工具 - 本地服务器版本
自动化处理 OAuth 2.0 PKCE 认证流程
"""

import hashlib
import uuid
import base64
import json
import webbrowser
import threading
import time
import argparse
import os
from urllib.parse import urlencode, urlparse, parse_qs
from http.server import HTTPServer, BaseHTTPRequestHandler
import requests


class AugmentOAuthConfig:
    """OAuth 配置类"""
    
    def __init__(self):
        self.client_id = "augment-vscode-extension"
        self.auth_url = "https://auth.augmentcode.com/authorize"
        self.token_url = "https://auth.augmentcode.com/token"  # 推测的token端点
        self.scope = "email"
        self.redirect_uri = "http://localhost:8080/callback"


class PKCEGenerator:
    """PKCE 参数生成器"""
    
    @staticmethod
    def base64url_encode(data):
        """Base64URL 编码"""
        return base64.urlsafe_b64encode(data).decode().rstrip("=")
    
    @classmethod
    def generate_pkce_pair(cls):
        """生成 PKCE 代码验证器和挑战"""
        code_verifier = cls.base64url_encode(hashlib.sha256(uuid.uuid4().bytes).digest())
        code_challenge = cls.base64url_encode(hashlib.sha256(code_verifier.encode()).digest())
        return code_verifier, code_challenge
    
    @staticmethod
    def generate_state():
        """生成状态参数"""
        return str(uuid.uuid4())


class OAuthCallbackHandler(BaseHTTPRequestHandler):
    """OAuth 回调处理器"""
    
    def __init__(self, *args, oauth_manager=None, **kwargs):
        self.oauth_manager = oauth_manager
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理 GET 请求"""
        if self.path.startswith('/callback'):
            self._handle_callback()
        else:
            self._handle_default()
    
    def _handle_callback(self):
        """处理 OAuth 回调"""
        try:
            # 解析查询参数
            parsed_url = urlparse(self.path)
            params = parse_qs(parsed_url.query)
            
            # 检查是否有错误
            if 'error' in params:
                error = params['error'][0]
                error_description = params.get('error_description', ['Unknown error'])[0]
                self._send_error_response(f"授权失败: {error} - {error_description}")
                self.oauth_manager.set_result('error', f"{error}: {error_description}")
                return
            
            # 获取授权码和状态
            if 'code' not in params or 'state' not in params:
                self._send_error_response("缺少必要的参数")
                self.oauth_manager.set_result('error', "Missing required parameters")
                return
            
            code = params['code'][0]
            state = params['state'][0]
            
            # 验证状态参数
            if state != self.oauth_manager.state:
                self._send_error_response("状态参数验证失败")
                self.oauth_manager.set_result('error', "State parameter mismatch")
                return
            
            # 发送成功响应
            self._send_success_response()
            
            # 设置结果
            self.oauth_manager.set_result('success', code)
            
        except Exception as e:
            self._send_error_response(f"处理回调时发生错误: {str(e)}")
            self.oauth_manager.set_result('error', str(e))
    
    def _handle_default(self):
        """处理默认请求"""
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Augment Code OAuth</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>Augment Code OAuth 认证</h1>
            <p>等待授权回调...</p>
        </body>
        </html>
        """
        self.wfile.write(html.encode('utf-8'))
    
    def _send_success_response(self):
        """发送成功响应"""
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>授权成功</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
                .success { color: green; }
            </style>
        </head>
        <body>
            <h1 class="success">✅ 授权成功！</h1>
            <p>您已成功授权 Augment Code 访问权限。</p>
            <p>您可以关闭此页面，返回终端查看结果。</p>
        </body>
        </html>
        """
        self.wfile.write(html.encode('utf-8'))
    
    def _send_error_response(self, error_message):
        """发送错误响应"""
        self.send_response(400)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>授权失败</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }}
                .error {{ color: red; }}
            </style>
        </head>
        <body>
            <h1 class="error">❌ 授权失败</h1>
            <p>{error_message}</p>
            <p>请返回终端查看详细信息。</p>
        </body>
        </html>
        """
        self.wfile.write(html.encode('utf-8'))
    
    def log_message(self, format, *args):
        """禁用默认日志输出"""
        pass


class TokenManager:
    """令牌管理器"""
    
    def __init__(self, token_file="tokens.json"):
        self.token_file = token_file
    
    def save_tokens(self, tokens):
        """保存令牌到文件"""
        try:
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(tokens, f, indent=2, ensure_ascii=False)
            
            # 设置文件权限（仅所有者可读写）
            if os.name != 'nt':  # 非Windows系统
                os.chmod(self.token_file, 0o600)
            
            print(f"✅ 令牌已保存到 {self.token_file}")
            return True
        except Exception as e:
            print(f"❌ 保存令牌失败: {e}")
            return False
    
    def load_tokens(self):
        """从文件加载令牌"""
        try:
            if not os.path.exists(self.token_file):
                return None
            
            with open(self.token_file, 'r', encoding='utf-8') as f:
                tokens = json.load(f)
            
            return tokens
        except Exception as e:
            print(f"⚠️ 加载令牌失败: {e}")
            return None
    
    def has_valid_tokens(self):
        """检查是否有有效令牌"""
        tokens = self.load_tokens()
        return tokens is not None and 'access_token' in tokens


class AugmentOAuthManager:
    """Augment OAuth 管理器"""
    
    def __init__(self, port=8080, config_file="tokens.json"):
        self.config = AugmentOAuthConfig()
        self.port = port
        self.token_manager = TokenManager(config_file)
        self.server = None
        self.result = None
        self.result_type = None
        self.state = None
        self.code_verifier = None
    
    def set_result(self, result_type, result):
        """设置认证结果"""
        self.result_type = result_type
        self.result = result
    
    def generate_auth_url(self):
        """生成授权URL"""
        # 生成PKCE参数
        self.code_verifier, code_challenge = PKCEGenerator.generate_pkce_pair()
        self.state = PKCEGenerator.generate_state()
        
        # 构造授权URL参数
        params = {
            "response_type": "code",
            "code_challenge": code_challenge,
            "code_challenge_method": "S256",
            "client_id": self.config.client_id,
            "redirect_uri": self.config.redirect_uri,
            "state": self.state,
            "scope": self.config.scope,
            "prompt": "login"
        }
        
        return f"{self.config.auth_url}?{urlencode(params)}"
    
    def start_server(self):
        """启动本地服务器"""
        try:
            # 创建处理器工厂
            def handler_factory(*args, **kwargs):
                return OAuthCallbackHandler(*args, oauth_manager=self, **kwargs)
            
            # 启动服务器
            self.server = HTTPServer(('localhost', self.port), handler_factory)
            print(f"🚀 本地服务器已启动: http://localhost:{self.port}")
            
            # 在新线程中运行服务器
            server_thread = threading.Thread(target=self.server.serve_forever)
            server_thread.daemon = True
            server_thread.start()
            
            return True
        except Exception as e:
            print(f"❌ 启动服务器失败: {e}")
            return False
    
    def stop_server(self):
        """停止服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("🛑 本地服务器已停止")

    def exchange_token(self, authorization_code):
        """交换访问令牌"""
        try:
            # 准备token交换请求
            data = {
                'grant_type': 'authorization_code',
                'client_id': self.config.client_id,
                'code': authorization_code,
                'redirect_uri': self.config.redirect_uri,
                'code_verifier': self.code_verifier
            }

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }

            print("🔄 正在交换访问令牌...")

            # 发送token交换请求
            response = requests.post(
                self.config.token_url,
                data=data,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                tokens = response.json()
                print("✅ 成功获取访问令牌")
                return tokens
            else:
                print(f"❌ Token交换失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ Token交换过程中发生错误: {e}")
            return None

    def wait_for_callback(self, timeout=300):
        """等待OAuth回调"""
        print("⏳ 等待授权回调...")
        print("请在浏览器中完成授权，然后返回此处查看结果")

        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.result is not None:
                return self.result_type, self.result
            time.sleep(0.5)

        return 'timeout', '等待超时'

    def authenticate(self):
        """执行完整的认证流程"""
        print("🔐 开始 Augment Code OAuth 认证流程")
        print("=" * 50)

        # 检查是否已有有效令牌
        if self.token_manager.has_valid_tokens():
            print("✅ 发现已保存的令牌")
            tokens = self.token_manager.load_tokens()
            print("令牌信息:")
            if 'access_token' in tokens:
                print(f"  - 访问令牌: {tokens['access_token'][:20]}...")
            if 'token_type' in tokens:
                print(f"  - 令牌类型: {tokens['token_type']}")
            if 'scope' in tokens:
                print(f"  - 权限范围: {tokens['scope']}")

            choice = input("\n是否使用已保存的令牌？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                return tokens
            else:
                print("将重新进行认证...")

        # 启动本地服务器
        if not self.start_server():
            return None

        try:
            # 生成授权URL
            auth_url = self.generate_auth_url()
            print(f"\n📱 授权URL已生成")
            print(f"🌐 正在打开浏览器进行授权...")

            # 打开浏览器
            if webbrowser.open(auth_url):
                print("✅ 浏览器已打开")
            else:
                print("⚠️ 无法自动打开浏览器，请手动访问以下URL:")
                print(f"   {auth_url}")

            # 等待回调
            result_type, result = self.wait_for_callback()

            if result_type == 'success':
                print("✅ 授权成功，正在获取访问令牌...")

                # 交换访问令牌
                tokens = self.exchange_token(result)
                if tokens:
                    # 保存令牌
                    if self.token_manager.save_tokens(tokens):
                        print("🎉 认证流程完成！")
                        return tokens
                    else:
                        print("⚠️ 令牌保存失败，但认证成功")
                        return tokens
                else:
                    print("❌ 获取访问令牌失败")
                    return None

            elif result_type == 'error':
                print(f"❌ 授权失败: {result}")
                return None

            elif result_type == 'timeout':
                print("⏰ 等待授权超时")
                return None

            else:
                print(f"❌ 未知错误: {result}")
                return None

        finally:
            # 停止服务器
            self.stop_server()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Augment Code OAuth 认证工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python augment_auth.py                    # 使用默认设置
  python augment_auth.py --port 9000       # 指定端口
  python augment_auth.py --config my.json  # 指定配置文件
        """
    )

    parser.add_argument(
        '--port',
        type=int,
        default=8080,
        help='本地服务器端口 (默认: 8080)'
    )

    parser.add_argument(
        '--config',
        default='tokens.json',
        help='令牌配置文件路径 (默认: tokens.json)'
    )

    parser.add_argument(
        '--show-token',
        action='store_true',
        help='显示完整的访问令牌'
    )

    args = parser.parse_args()

    try:
        # 创建OAuth管理器
        oauth_manager = AugmentOAuthManager(
            port=args.port,
            config_file=args.config
        )

        # 执行认证
        tokens = oauth_manager.authenticate()

        if tokens:
            print("\n" + "=" * 50)
            print("🎉 认证成功！")
            print("=" * 50)

            if args.show_token and 'access_token' in tokens:
                print(f"访问令牌: {tokens['access_token']}")

            print("\n📋 您现在可以使用以下信息调用 Augment Code API:")
            print(f"  - 配置文件: {args.config}")
            if 'access_token' in tokens:
                print(f"  - 令牌类型: Bearer")
                print(f"  - Authorization Header: Bearer {tokens['access_token'][:20]}...")

            print(f"\n💡 提示: 令牌已保存到 {args.config}，下次运行时会自动加载")

        else:
            print("\n❌ 认证失败")
            return 1

    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
